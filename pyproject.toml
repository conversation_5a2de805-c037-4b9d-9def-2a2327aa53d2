[project]
name = "jupymcp"
version = "0.1.2"
description = "Jupyter + MCP"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON> (唐梓涯)", email = "<EMAIL>" },
]
requires-python = ">=3.11"
dependencies = [
    "jupyter-kernel-client>=0.8.0",
    "jupyter-nbmodel-client>=0.14.0",
    "mcp>=1.13.1",
]
[project.scripts]
jupymcp = "jupymcp:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
